///
/// @file BranchCondInstruction.cpp
/// @brief 有条件分支指令（条件跳转）实现
///
/// <AUTHOR>
/// @version 1.0
/// @date 2025-05-XX
///
#include "VoidType.h"
#include "BranchCondInstruction.h"

BranchCondInstruction::BranchCondInstruction(Function * _func,
                                             IRInstOperator _op,
                                             Value * _condValue,
                                             LabelInstruction * _trueLabel,
                                             LabelInstruction * _falseLabel)
    : Instruction(_func, _op, VoidType::getType()), condValue(_condValue), trueLabel(_trueLabel),
      falseLabel(_falseLabel)
{
    addOperand(condValue);
}

void BranchCondInstruction::toString(std::string & str)
{
    // 根据 op 输出不同的指令名
    std::string opName;
    switch (getOp()) {
        case IRInstOperator::IRINST_OP_BC_LT:
            opName = "bc_lt";
            break;
        case IRInstOperator::IRINST_OP_BC_GT:
            opName = "bc_gt";
            break;
        case IRInstOperator::IRINST_OP_BC_LE:
            opName = "bc_le";
            break;
        case IRInstOperator::IRINST_OP_BC_GE:
            opName = "bc_ge";
            break;
        case IRInstOperator::IRINST_OP_BC_EQ:
            opName = "bc_eq";
            break;
        case IRInstOperator::IRINST_OP_BC_NE:
            opName = "bc_ne";
            break;
        default:
            opName = "bc";
            break;
    }
    str = "bc" + condValue->getIRName() + ", label " + trueLabel->getIRName() + ", label " + falseLabel->getIRName();
}

LabelInstruction * BranchCondInstruction::getTrueLabel() const
{
    return trueLabel;
}
LabelInstruction * BranchCondInstruction::getFalseLabel() const
{
    return falseLabel;
}
