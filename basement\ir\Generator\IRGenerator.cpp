///
/// @file IRGenerator.cpp
/// @brief AST遍历产生线性IR的源文件
/// <AUTHOR> (<EMAIL>)
/// @version 1.1
/// @date 2024-11-23
///
/// @copyright Copyright (c) 2024
///
/// @par 修改日志:
/// <table>
/// <tr><th>Date       <th>Version <th>Author  <th>Description
/// <tr><td>2024-09-29 <td>1.0     <td>zenglj  <td>新建
/// <tr><td>2024-11-23 <td>1.1     <td>zenglj  <td>表达式版增强
/// </table>
///
#include <cstdint>
#include <cstdio>
#include <unordered_map>
#include <vector>
#include <iostream>
#include <cassert>

#include "AST.h"
#include "Common.h"
#include "Function.h"
#include "IRCode.h"
#include "IRGenerator.h"
#include "Module.h"
#include "EntryInstruction.h"

#include "ExitInstruction.h"
#include "FuncCallInstruction.h"
#include "BinaryInstruction.h"
#include "MoveInstruction.h"
#include "GotoInstruction.h"
#include "CmpInstruction.h"
#include "BranchCondInstruction.h"
#include "StoreInstruction.h"
#include "LoadInstruction.h"
#include "ArrayType.h"
#include "PointerType.h"

/// @brief 构造函数
/// @param _root AST的根
/// @param _module 符号表
IRGenerator::IRGenerator(ast_node * _root, Module * _module) : root(_root), module(_module)
{
    /* 叶子节点 */
    ast2ir_handlers[ast_operator_type::AST_OP_LEAF_LITERAL_UINT] = &IRGenerator::ir_leaf_node_uint;
    ast2ir_handlers[ast_operator_type::AST_OP_LEAF_VAR_ID] = &IRGenerator::ir_leaf_node_var_id;
    ast2ir_handlers[ast_operator_type::AST_OP_LEAF_TYPE] = &IRGenerator::ir_leaf_node_type;

    /* 表达式运算， 加减 */
    ast2ir_handlers[ast_operator_type::AST_OP_SUB] = &IRGenerator::ir_sub;
    ast2ir_handlers[ast_operator_type::AST_OP_ADD] = &IRGenerator::ir_add;

    /* 表达式运算， 乘除余 */
    ast2ir_handlers[ast_operator_type::AST_OP_MUL] = &IRGenerator::ir_mul;
    ast2ir_handlers[ast_operator_type::AST_OP_DIV] = &IRGenerator::ir_div;
    ast2ir_handlers[ast_operator_type::AST_OP_MOD] = &IRGenerator::ir_mod;

    /* 表达式运算， 自运算 */
    ast2ir_handlers[ast_operator_type::AST_OP_PRE_INC] = &IRGenerator::ir_pre_inc_dec;
    ast2ir_handlers[ast_operator_type::AST_OP_PRE_DEC] = &IRGenerator::ir_pre_inc_dec;
    ast2ir_handlers[ast_operator_type::AST_OP_POST_INC] = &IRGenerator::ir_post_inc_dec;
    ast2ir_handlers[ast_operator_type::AST_OP_POST_DEC] = &IRGenerator::ir_post_inc_dec;

    /* 比较操作 */
    ast2ir_handlers[ast_operator_type::AST_OP_CMP_LT] = &IRGenerator::ir_cmp;
    ast2ir_handlers[ast_operator_type::AST_OP_CMP_GT] = &IRGenerator::ir_cmp;
    ast2ir_handlers[ast_operator_type::AST_OP_CMP_LE] = &IRGenerator::ir_cmp;
    ast2ir_handlers[ast_operator_type::AST_OP_CMP_GE] = &IRGenerator::ir_cmp;
    ast2ir_handlers[ast_operator_type::AST_OP_CMP_EQ] = &IRGenerator::ir_cmp;
    ast2ir_handlers[ast_operator_type::AST_OP_CMP_NE] = &IRGenerator::ir_cmp;

    /* 条件跳转 */
    ast2ir_handlers[ast_operator_type::AST_OP_BC] = &IRGenerator::ir_bc;
    ast2ir_handlers[ast_operator_type::AST_OP_BC_LT] = &IRGenerator::ir_bc;
    ast2ir_handlers[ast_operator_type::AST_OP_BC_GT] = &IRGenerator::ir_bc;
    ast2ir_handlers[ast_operator_type::AST_OP_BC_LE] = &IRGenerator::ir_bc;
    ast2ir_handlers[ast_operator_type::AST_OP_BC_GE] = &IRGenerator::ir_bc;
    ast2ir_handlers[ast_operator_type::AST_OP_BC_EQ] = &IRGenerator::ir_bc;
    ast2ir_handlers[ast_operator_type::AST_OP_BC_NE] = &IRGenerator::ir_bc;

    ast2ir_handlers[ast_operator_type::AST_OP_NOT] = &IRGenerator::ir_not;
    ast2ir_handlers[ast_operator_type::AST_OP_AND] = &IRGenerator::ir_and;
    ast2ir_handlers[ast_operator_type::AST_OP_OR] = &IRGenerator::ir_or;

    /*分支循环语句*/
    ast2ir_handlers[ast_operator_type::AST_OP_IF] = &IRGenerator::ir_if;
    ast2ir_handlers[ast_operator_type::AST_OP_WHILE] = &IRGenerator::ir_while;
    ast2ir_handlers[ast_operator_type::AST_OP_BREAK] = &IRGenerator::ir_break;
    ast2ir_handlers[ast_operator_type::AST_OP_CONTINUE] = &IRGenerator::ir_continue;
    ast2ir_handlers[ast_operator_type::AST_OP_FOR] = &IRGenerator::ir_for;

    /* 语句 */
    ast2ir_handlers[ast_operator_type::AST_OP_ASSIGN] = &IRGenerator::ir_assign;
    ast2ir_handlers[ast_operator_type::AST_OP_RETURN] = &IRGenerator::ir_return;

    /* 函数调用 */
    ast2ir_handlers[ast_operator_type::AST_OP_FUNC_CALL] = &IRGenerator::ir_function_call;

    /* 函数定义 */
    ast2ir_handlers[ast_operator_type::AST_OP_FUNC_DEF] = &IRGenerator::ir_function_define;
    ast2ir_handlers[ast_operator_type::AST_OP_FUNC_FORMAL_PARAMS] = &IRGenerator::ir_function_formal_params;

    /* 变量定义语句 */
    ast2ir_handlers[ast_operator_type::AST_OP_DECL_STMT] = &IRGenerator::ir_declare_statment;
    ast2ir_handlers[ast_operator_type::AST_OP_VAR_DECL] = &IRGenerator::ir_variable_declare;
	/* 数组读语句 */
    ast2ir_handlers[ast_operator_type::AST_OP_ARRAY_ACCESS] = &IRGenerator::ir_array_access;

    /* 语句块 */
    ast2ir_handlers[ast_operator_type::AST_OP_BLOCK] = &IRGenerator::ir_block;

    /* 编译单元 */
    ast2ir_handlers[ast_operator_type::AST_OP_COMPILE_UNIT] = &IRGenerator::ir_compile_unit;
}

// 辅助：将 CMP 操作码映射到对应的 BC_* 操作码
static IRInstOperator mapCmpToBc(IRInstOperator cmpOp)
{
    switch (cmpOp) {
        case IRInstOperator::IRINST_OP_CMP_LT:
            return IRInstOperator::IRINST_OP_BC_LT;
        case IRInstOperator::IRINST_OP_CMP_GT:
            return IRInstOperator::IRINST_OP_BC_GT;
        case IRInstOperator::IRINST_OP_CMP_LE:
            return IRInstOperator::IRINST_OP_BC_LE;
        case IRInstOperator::IRINST_OP_CMP_GE:
            return IRInstOperator::IRINST_OP_BC_GE;
        case IRInstOperator::IRINST_OP_CMP_EQ:
            return IRInstOperator::IRINST_OP_BC_EQ;
        case IRInstOperator::IRINST_OP_CMP_NE:
            return IRInstOperator::IRINST_OP_BC_NE;
        default:
            return IRInstOperator::IRINST_OP_BC; // 保持兼容
    }
}

/// @brief 遍历抽象语法树产生线性IR，保存到IRCode中
/// @param root 抽象语法树
/// @param IRCode 线性IR
/// @return true: 成功 false: 失败
bool IRGenerator::run()
{
    ast_node * node;

    // 从根节点进行遍历
    node = ir_visit_ast_node(root);

    return node != nullptr;
}

/// @brief 根据AST的节点运算符查找对应的翻译函数并执行翻译动作
/// @param node AST节点
/// @return 成功返回node节点，否则返回nullptr
ast_node * IRGenerator::ir_visit_ast_node(ast_node * node)
{
    // 空节点
    if (nullptr == node) {
        return nullptr;
    }

    bool result;

    std::unordered_map<ast_operator_type, ast2ir_handler_t>::const_iterator pIter;
    pIter = ast2ir_handlers.find(node->node_type);
    if (pIter == ast2ir_handlers.end()) {
        // 没有找到，则说明当前不支持
        result = (this->ir_default)(node);
    } else {
        result = (this->*(pIter->second))(node);
    }

    if (!result) {
        // 语义解析错误，则出错返回
        node = nullptr;
    }

    return node;
}

/// @brief 未知节点类型的节点处理
/// @param node AST节点
/// @return 翻译是否成功，true：成功，false：失败
bool IRGenerator::ir_default(ast_node * node)
{
    // 未知的节点
    printf("Unkown node(%d)\n", (int) node->node_type);
    return true;
}

/// @brief 编译单元AST节点翻译成线性中间IR
/// @param node AST节点
/// @return 翻译是否成功，true：成功，false：失败
bool IRGenerator::ir_compile_unit(ast_node * node)
{
    module->setCurrentFunction(nullptr);

    for (auto son: node->sons) {

        // 遍历编译单元，要么是函数定义，要么是语句
        ast_node * son_node = ir_visit_ast_node(son);
        if (!son_node) {
            // TODO 自行追加语义错误处理
            return false;
        }
    }

    return true;
}

/// @brief 函数定义AST节点翻译成线性中间IR
/// @param node AST节点
/// @return 翻译是否成功，true：成功，false：失败
bool IRGenerator::ir_function_define(ast_node * node)
{
    bool result;

    // 创建一个函数，用于当前函数处理
    if (module->getCurrentFunction()) {
        // 函数中嵌套定义函数，这是不允许的，错误退出
        // TODO 自行追加语义错误处理
        return false;
    }

    // 函数定义的AST包含四个孩子
    // 第一个孩子：函数返回类型
    // 第二个孩子：函数名字
    // 第三个孩子：形参列表
    // 第四个孩子：函数体即block
    ast_node * type_node = node->sons[0];
    ast_node * name_node = node->sons[1];
    ast_node * param_node = node->sons[2];
    ast_node * block_node = node->sons[3];

    // 创建一个新的函数定义
    Function * newFunc = module->newFunction(name_node->name, type_node->type);
    if (!newFunc) {
        // 新定义的函数已经存在，则失败返回。
        // TODO 自行追加语义错误处理
        return false;
    }

    // 当前函数设置有效，变更为当前的函数
    module->setCurrentFunction(newFunc);

    // 进入函数的作用域
    module->enterScope();

    // 获取函数的IR代码列表，用于后面追加指令用，注意这里用的是引用传值
    InterCode & irCode = newFunc->getInterCode();

    // 这里也可增加一个函数入口Label指令，便于后续基本块划分

    // 创建并加入Entry入口指令
    irCode.addInst(new EntryInstruction(newFunc));

    // 创建出口指令并不加入出口指令，等函数内的指令处理完毕后加入出口指令
    LabelInstruction * exitLabelInst = new LabelInstruction(newFunc);

    // 函数出口指令保存到函数信息中，因为在语义分析函数体时return语句需要跳转到函数尾部，需要这个label指令
    newFunc->setExitLabel(exitLabelInst);

    for (int i = 0; i < (int) param_node->sons.size(); ++i) {
        ast_node * paramAst = param_node->sons[i];

        Type * ty = paramAst->sons[0]->type;
        std::string name = paramAst->sons[1]->sons[0]->name; // 注意是 .sons[1]->sons[0]

        Type * rty = nullptr;
        // 创建一个 FormalParam 实例，作为函数调用入口处传入的“临时值”
        if (paramAst->sons[1]->sons.size() >= 2) {
            ast_node * dimsNode = paramAst->sons[1]->sons[1];
            std::vector<int> dims;

            // 提取维度信息
            for (size_t j = 0; j < dimsNode->sons.size(); ++j) {
                int dim = static_cast<int>(dimsNode->sons[j]->integer_val);
                if (j == 0) {
                    dim = 0; // 第一维设置为 0
                }
                dims.push_back(dim);
            }

            // 构造数组类型
            rty = Type::getArray(ty, dims);
        } else {
            rty = ty; // 普通形参类型
        }
        // 获取类型与形参名
        FormalParam * argVal = new FormalParam(rty, name);
        // 设置 paramAst->val 为这个形参的临时值
        paramAst->val = argVal;
        newFunc->getParams().push_back(argVal);
    }

    // 遍历形参，没有IR指令，不需要追加
    result = ir_function_formal_params(param_node);
    if (!result) {
        // 形参解析失败
        // TODO 自行追加语义错误处理
        return false;
    }
    node->blockInsts.addInst(param_node->blockInsts);

    // 新建一个Value，用于保存函数的返回值，如果没有返回值可不用申请
    LocalVariable * retValue = nullptr;
    if (!type_node->type->isVoidType()) {

        // 保存函数返回值变量到函数信息中，在return语句翻译时需要设置值到这个变量中
        retValue = static_cast<LocalVariable *>(module->newVarValue(type_node->type));
    }
    newFunc->setReturnValue(retValue);

    // 这里最好设置返回值变量的初值为0，以便在没有返回值时能够返回0
    if (name_node->name == "main" && retValue) {
        irCode.addInst(new MoveInstruction(newFunc, retValue, module->newConstInt(0)));
    }

    // 函数内已经进入作用域，内部不再需要做变量的作用域管理
    block_node->needScope = false;

    // 遍历block
    result = ir_block(block_node);
    if (!result) {
        // block解析失败
        // TODO 自行追加语义错误处理
        return false;
    }

    // IR指令追加到当前的节点中
    node->blockInsts.addInst(block_node->blockInsts);

    // 此时，所有指令都加入到当前函数中，也就是node->blockInsts

    // node节点的指令移动到函数的IR指令列表中
    irCode.addInst(node->blockInsts);

    // 添加函数出口Label指令，主要用于return语句跳转到这里进行函数的退出
    irCode.addInst(exitLabelInst);

    // 函数出口指令
    irCode.addInst(new ExitInstruction(newFunc, retValue));

    // 恢复成外部函数
    module->setCurrentFunction(nullptr);

    // 退出函数的作用域
    module->leaveScope();

    return true;
}

/// @brief 形式参数AST节点翻译成线性中间IR
/// @param node AST节点
/// @return 翻译是否成功，true：成功，false：失败
bool IRGenerator::ir_function_formal_params(ast_node * node)
{
    if (!node)
        return true;

    Function * curF = module->getCurrentFunction();

    for (auto * paramAst: node->sons) {
        Type * elemTy = paramAst->sons[0]->type; // 元素类型
        std::string name = paramAst->sons[1]->sons[0]->name;

        if (paramAst->sons[1]->sons.size() >= 2) {
            // 数组形参，第一维为 0
            ast_node * dimsNode = paramAst->sons[1]->sons[1];
            std::vector<int> dims;

            // 提取维度信息
            for (size_t i = 0; i < dimsNode->sons.size(); ++i) {
                int dim = static_cast<int>(dimsNode->sons[i]->integer_val);
                if (i == 0) {
                    dim = 0; // 第一维设置为 0
                }
                dims.push_back(dim);
            }

            // 构造数组类型
            ArrayType * arrTy = Type::getArray(elemTy, dims);

            // 转换为指针类型
            auto * localVar = static_cast<LocalVariable *>(module->newVarValue(arrTy, name));

            // 获取传入的原始临时变量（如 %t0）
            Value * tempVal = paramAst->val;

            // 生成 move 指令： %lX = %tX
            node->blockInsts.addInst(new MoveInstruction(curF, localVar, tempVal));

            paramAst->val = localVar;
        } else {
            // 普通形参
            auto * localVar = static_cast<LocalVariable *>(module->newVarValue(elemTy, name));
            Value * tempVal = paramAst->val;
            node->blockInsts.addInst(new MoveInstruction(curF, localVar, tempVal));
            paramAst->val = localVar;
        }
    }

    return true;
}

/// @brief 函数调用AST节点翻译成线性中间IR
/// @param node AST节点
/// @return 翻译是否成功，true：成功，false：失败
bool IRGenerator::ir_function_call(ast_node * node)
{
    std::vector<Value *> realParams;

    // 获取当前正在处理的函数
    Function * currentFunc = module->getCurrentFunction();

    // 函数调用的节点包含两个节点：
    // 第一个节点：函数名节点
    // 第二个节点：实参列表节点

    std::string funcName = node->sons[0]->name;
    int64_t lineno = node->sons[0]->line_no;

    ast_node * paramsNode = node->sons[1];

    // 根据函数名查找函数，看是否存在。若不存在则出错
    // 这里约定函数必须先定义后使用
    auto calledFunction = module->findFunction(funcName);
    if (nullptr == calledFunction) {
        minic_log(LOG_ERROR, "函数(%s)未定义或声明", funcName.c_str());
        return false;
    }

    // 当前函数存在函数调用
    currentFunc->setExistFuncCall(true);

    // 如果没有孩子，也认为是没有参数
    if (!paramsNode->sons.empty()) {

        int32_t argsCount = (int32_t) paramsNode->sons.size();

        // 当前函数中调用函数实参个数最大值统计，实际上是统计实参传参需在栈中分配的大小
        // 因为目前的语言支持的int和float都是四字节的，只统计个数即可
        if (argsCount > currentFunc->getMaxFuncCallArgCnt()) {
            currentFunc->setMaxFuncCallArgCnt(argsCount);
        }

        // 遍历参数列表，孩子是表达式
        // 这里自左往右计算表达式
        for (auto son: paramsNode->sons) {
            ast_node * temp = ir_visit_ast_node(son);
            if (!temp)
                return false;
            Value * baseVal = nullptr;
            if (temp->node_type == ast_operator_type::AST_OP_ARRAY_ACCESS) {
                realParams.push_back(temp->val);
            } else {
                baseVal = module->findVarValue(temp->name);
                // 如果是数组形参，传递地址
                if (dynamic_cast<ArrayType *>(temp->val->getType())) {
                    realParams.push_back(baseVal);
                } else {
                    realParams.push_back(temp->val);
                }
            }

            node->blockInsts.addInst(temp->blockInsts);
        }
    }

    // TODO 这里请追加函数调用的语义错误检查，这里只进行了函数参数的个数检查等，其它请自行追加。
    if (realParams.size() != calledFunction->getParams().size()) {
        // 函数参数的个数不一致，语义错误
        minic_log(LOG_ERROR, "第%lld行的被调用函数(%s)未定义或声明", (long long) lineno, funcName.c_str());
        return false;
    }

    // 返回调用有返回值，则需要分配临时变量，用于保存函数调用的返回值
    Type * type = calledFunction->getReturnType();

    FuncCallInstruction * funcCallInst = new FuncCallInstruction(currentFunc, calledFunction, realParams, type);

    // 创建函数调用指令
    node->blockInsts.addInst(funcCallInst);

    // 函数调用结果Value保存到node中，可能为空，上层节点可利用这个值
    node->val = funcCallInst;

    return true;
}

/// @brief 语句块（含函数体）AST节点翻译成线性中间IR
/// @param node AST节点
/// @return 翻译是否成功，true：成功，false：失败
bool IRGenerator::ir_block(ast_node * node)
{
    // 进入作用域
    if (node->needScope) {
        module->enterScope();
    }

    std::vector<ast_node *>::iterator pIter;
    for (pIter = node->sons.begin(); pIter != node->sons.end(); ++pIter) {

        // 遍历Block的每个语句，进行显示或者运算
        ast_node * temp = ir_visit_ast_node(*pIter);
        if (!temp) {
            return false;
        }

        node->blockInsts.addInst(temp->blockInsts);
    }

    // 离开作用域
    if (node->needScope) {
        module->leaveScope();
    }

    return true;
}

/// @brief 整数加法AST节点翻译成线性中间IR
/// @param node AST节点
/// @return 翻译是否成功，true：成功，false：失败
bool IRGenerator::ir_add(ast_node * node)
{
    ast_node * src1_node = node->sons[0];
    ast_node * src2_node = node->sons[1];

    // 加法节点，左结合，先计算左节点，后计算右节点

    // 加法的左边操作数
    ast_node * left = ir_visit_ast_node(src1_node);
    if (!left) {
        // 某个变量没有定值
        return false;
    }

    // 加法的右边操作数
    ast_node * right = ir_visit_ast_node(src2_node);
    if (!right) {
        // 某个变量没有定值
        return false;
    }

    // 这里只处理整型的数据，如需支持实数，则需要针对类型进行处理

    BinaryInstruction * addInst = new BinaryInstruction(module->getCurrentFunction(),
                                                        IRInstOperator::IRINST_OP_ADD_I,
                                                        left->val,
                                                        right->val,
                                                        IntegerType::getTypeInt());

    // 创建临时变量保存IR的值，以及线性IR指令
    node->blockInsts.addInst(left->blockInsts);
    node->blockInsts.addInst(right->blockInsts);
    node->blockInsts.addInst(addInst);

    node->val = addInst;

    return true;
}

/// @brief 整数减法AST节点翻译成线性中间IR
/// @param node AST节点
/// @return 翻译是否成功，true：成功，false：失败
bool IRGenerator::ir_sub(ast_node * node)
{
    ast_node * src1_node = node->sons[0];
    ast_node * src2_node = node->sons[1];

    // 加法节点，左结合，先计算左节点，后计算右节点

    // 加法的左边操作数
    ast_node * left = ir_visit_ast_node(src1_node);
    if (!left) {
        // 某个变量没有定值
        return false;
    }

    // 加法的右边操作数
    ast_node * right = ir_visit_ast_node(src2_node);
    if (!right) {
        // 某个变量没有定值
        return false;
    }

    // 这里只处理整型的数据，如需支持实数，则需要针对类型进行处理

    BinaryInstruction * subInst = new BinaryInstruction(module->getCurrentFunction(),
                                                        IRInstOperator::IRINST_OP_SUB_I,
                                                        left->val,
                                                        right->val,
                                                        IntegerType::getTypeInt());

    // 创建临时变量保存IR的值，以及线性IR指令
    node->blockInsts.addInst(left->blockInsts);
    node->blockInsts.addInst(right->blockInsts);
    node->blockInsts.addInst(subInst);

    node->val = subInst;

    return true;
}

/// @brief 整数乘法AST节点翻译成线性中间IR
/// @param node AST节点
/// @return 翻译是否成功，true：成功，false：失败
bool IRGenerator::ir_mul(ast_node * node)
{
    // 获取乘法表达式的左右子节点
    // 在AST中，二元运算符节点有两个子节点：sons[0]为左操作数，sons[1]为右操作数
    ast_node * src1_node = node->sons[0];  // 左操作数AST节点
    ast_node * src2_node = node->sons[1];  // 右操作数AST节点

    // 乘法运算遵循左结合性，按照从左到右的顺序计算操作数
    // 这确保了表达式如 a * b * c 被解析为 (a * b) * c

    // 递归处理乘法的左边操作数
    // ir_visit_ast_node会递归生成左操作数的IR代码，并返回包含结果值的AST节点
    ast_node * left = ir_visit_ast_node(src1_node);
    if (!left) {
        // 左操作数处理失败，可能原因：
        // 1. 使用了未定义的变量
        // 2. 子表达式计算出错
        // 3. 类型不匹配等语义错误
        return false;
    }

    // 递归处理乘法的右边操作数
    // 同样递归生成右操作数的IR代码
    ast_node * right = ir_visit_ast_node(src2_node);
    if (!right) {
        // 右操作数处理失败，错误原因同左操作数
        return false;
    }

    // 类型限制说明：当前实现仅支持整型数据的乘法运算
    // 如果将来需要支持浮点数乘法，需要：
    // 1. 检查操作数的具体类型（int, float, double等）
    // 2. 根据类型选择相应的IR指令操作符（如IRINST_OP_MUL_F用于浮点乘法）
    // 3. 处理类型提升和隐式转换

    // 创建二元乘法指令
    // BinaryInstruction表示二元运算的IR指令，包含以下参数：
    // - 当前函数对象：指令所属的函数
    // - 操作符：IRINST_OP_MUL_I表示整数乘法
    // - 左操作数：left->val是左子表达式计算结果的Value对象
    // - 右操作数：right->val是右子表达式计算结果的Value对象
    // - 结果类型：IntegerType::getTypeInt()表示32位整数类型
    BinaryInstruction * mulInst = new BinaryInstruction(module->getCurrentFunction(),
                                                        IRInstOperator::IRINST_OP_MUL_I,
                                                        left->val,
                                                        right->val,
                                                        IntegerType::getTypeInt());

    // 按照正确的执行顺序组装IR指令序列
    // IR指令必须按照数据依赖关系的顺序排列，确保：
    // 1. 先执行左操作数的所有指令（计算左子表达式）
    // 2. 再执行右操作数的所有指令（计算右子表达式）
    // 3. 最后执行乘法指令（使用前两步的结果）
    node->blockInsts.addInst(left->blockInsts);   // 添加左操作数的指令序列
    node->blockInsts.addInst(right->blockInsts);  // 添加右操作数的指令序列
    node->blockInsts.addInst(mulInst);            // 添加乘法指令

    // 设置当前节点的值为乘法指令的结果
    // 这个值可以被父节点或后续指令使用
    node->val = mulInst;
    return true;  // 成功生成乘法运算的IR代码
}

/// @brief 有符号整数除法AST节点翻译成线性中间IR
/// @param node AST节点
/// @return 翻译是否成功，true：成功，false：失败
bool IRGenerator::ir_div(ast_node * node)
{
    // 获取除法表达式的左右子节点
    // 在除法运算中，sons[0]是被除数（dividend），sons[1]是除数（divisor）
    ast_node * src1_node = node->sons[0];  // 被除数AST节点
    ast_node * src2_node = node->sons[1];  // 除数AST节点

    // 除法运算同样遵循左结合性，按照从左到右的顺序计算操作数
    // 对于表达式 a / b / c，会被解析为 (a / b) / c

    // 递归处理除法的左边操作数（被除数）
    // 生成被除数表达式的IR代码
    ast_node * left = ir_visit_ast_node(src1_node);
    if (!left) {
        // 被除数处理失败，可能的错误原因：
        // 1. 被除数表达式中包含未定义的变量
        // 2. 被除数子表达式计算错误
        // 3. 类型不兼容等语义错误
        return false;
    }

    // 递归处理除法的右边操作数（除数）
    // 生成除数表达式的IR代码
    ast_node * right = ir_visit_ast_node(src2_node);
    if (!right) {
        // 除数处理失败，错误原因类似被除数
        // 注意：这里不检查除零错误，除零检查通常在运行时进行
        // 或者在更高级的优化阶段进行常量折叠时检查
        return false;
    }

    // 创建有符号整数除法指令
    // 使用IRINST_OP_SDIV_I操作符表示有符号整数除法，区别于无符号除法：
    // - 有符号除法：考虑操作数的符号位，结果的符号遵循数学规则
    //   例如：(-8) / 3 = -2, 8 / (-3) = -2, (-8) / (-3) = 2
    // - 无符号除法：将操作数视为无符号数进行除法运算
    //
    // 除法指令的参数说明：
    // - 当前函数：指令所属的函数上下文
    // - 操作符：IRINST_OP_SDIV_I表示有符号整数除法
    // - 被除数：left->val是左子表达式的计算结果
    // - 除数：right->val是右子表达式的计算结果
    // - 结果类型：整数类型，与操作数类型保持一致
    BinaryInstruction * divInst = new BinaryInstruction(module->getCurrentFunction(),
                                                        IRInstOperator::IRINST_OP_SDIV_I,
                                                        left->val,
                                                        right->val,
                                                        IntegerType::getTypeInt());

    // 按照数据依赖关系组装IR指令序列
    // 除法运算的执行顺序必须严格遵循：
    // 1. 首先计算被除数（左操作数）
    // 2. 然后计算除数（右操作数）
    // 3. 最后执行除法运算
    // 这个顺序确保了在执行除法指令时，所需的操作数都已经计算完成
    node->blockInsts.addInst(left->blockInsts);   // 添加被除数的指令序列
    node->blockInsts.addInst(right->blockInsts);  // 添加除数的指令序列
    node->blockInsts.addInst(divInst);            // 添加除法指令

    // 设置当前节点的值为除法指令的结果
    // 这个结果值可以被后续的表达式或语句使用
    node->val = divInst;
    return true;  // 成功生成除法运算的IR代码
}

/// @brief 取余AST节点翻译成线性中间IR（a % b → a – (a / b) * b）
/// @param node AST节点
/// @return 翻译是否成功，true：成功，false：失败
bool IRGenerator::ir_mod(ast_node * node)
{
    // 取余运算的数学定义：a % b = a - (a / b) * b
    // 其中 (a / b) 是整数除法，结果向零截断
    // 例如：17 % 5 = 17 - (17/5)*5 = 17 - 3*5 = 17 - 15 = 2
    //      -17 % 5 = -17 - (-17/5)*5 = -17 - (-3)*5 = -17 - (-15) = -17 + 15 = -2

    // 1. 递归生成左右子表达式的IR代码，获取操作数的Value对象
    ast_node * lhsNode = node->sons[0];  // 被除数（左操作数）AST节点
    ast_node * rhsNode = node->sons[1];  // 除数（右操作数）AST节点

    // 处理左操作数（被除数）
    ast_node * left = ir_visit_ast_node(lhsNode);
    if (!left) {
        // 左操作数处理失败，可能原因：
        // 1. 表达式中包含未定义的变量
        // 2. 子表达式计算错误
        // 3. 类型不匹配等语义错误
        return false;
    }

    // 处理右操作数（除数）
    ast_node * right = ir_visit_ast_node(rhsNode);
    if (!right) {
        // 右操作数处理失败，错误原因同左操作数
        // 注意：这里不进行除零检查，除零通常在运行时检测
        return false;
    }

    // 2. 保存原始操作数的Value对象，避免重复调用ir_visit_ast_node
    // 在后续的多个指令中需要重复使用这些值，保存引用可以提高效率
    // 并确保使用的是同一个Value对象
    Value * aVal = left->val;   // 原始被除数的值
    Value * bVal = right->val;  // 原始除数的值

    // 3. 生成第一步：整数除法指令 q = a / b
    // 这里使用有符号整数除法，结果向零截断
    // 例如：17 / 5 = 3, -17 / 5 = -3
    BinaryInstruction * divInst = new BinaryInstruction(module->getCurrentFunction(),
                                                        IRInstOperator::IRINST_OP_SDIV_I,
                                                        aVal,  // 被除数
                                                        bVal,  // 除数
                                                        IntegerType::getTypeInt());

    // 按照执行顺序添加IR指令到指令序列中
    // 必须先添加操作数的计算指令，再添加当前运算指令
    node->blockInsts.addInst(left->blockInsts);   // 左操作数的指令序列
    node->blockInsts.addInst(right->blockInsts);  // 右操作数的指令序列
    node->blockInsts.addInst(divInst);            // 除法指令

    // 4. 生成第二步：乘法指令 prod = q * b
    // 将除法的商与原除数相乘，得到能被除数整除的最大值
    // 例如：3 * 5 = 15, (-3) * 5 = -15
    BinaryInstruction * mulInst = new BinaryInstruction(module->getCurrentFunction(),
                                                        IRInstOperator::IRINST_OP_MUL_I,
                                                        divInst,  // 使用上一步除法指令的结果作为左操作数
                                                        bVal,     // 原除数作为右操作数
                                                        IntegerType::getTypeInt());
    node->blockInsts.addInst(mulInst);  // 添加乘法指令

    // 5. 生成第三步：减法指令 rem = a - prod
    // 用原被除数减去上一步的乘积，得到最终的余数
    // 例如：17 - 15 = 2, -17 - (-15) = -17 + 15 = -2
    BinaryInstruction * subInst = new BinaryInstruction(module->getCurrentFunction(),
                                                        IRInstOperator::IRINST_OP_SUB_I,
                                                        aVal,     // 使用最初保存的原被除数
                                                        mulInst,  // 使用上一步乘法指令的结果
                                                        IntegerType::getTypeInt());
    node->blockInsts.addInst(subInst);  // 添加减法指令

    // 6. 设置最终结果
    // 减法指令的结果就是取余运算的最终结果
    // 整个取余运算通过三个基本运算（除法、乘法、减法）的组合来实现
    node->val = subInst;
    return true;  // 成功生成取余运算的IR代码
}

/// @brief 赋值AST节点翻译成线性中间IR
/// @param node AST节点
/// @return 翻译是否成功，true：成功，false：失败
bool IRGenerator::ir_assign(ast_node * node)
{
    ast_node * leftNode = node->sons[0];
    ast_node * rightNode = node->sons[1];

    ast_node * left = ir_visit_ast_node(leftNode);
    if (!left)
        return false;

    ast_node * right = ir_visit_ast_node(rightNode);
    if (!right)
        return false;

    node->blockInsts.addInst(right->blockInsts);
    node->blockInsts.addInst(left->blockInsts);

    Function * curF = module->getCurrentFunction();

    // 情况一：左边是数组访问（例如 a[k] = m）
    if (leftNode->node_type == ast_operator_type::AST_OP_ARRAY_ACCESS) {
        // 生成 StoreInstruction，写入到 left->val（即地址）中
        auto * store = new StoreInstruction(curF, left->ptrval, right->val);
        node->blockInsts.addInst(store);
        node->val = right->val;
    }
    // 情况二：普通赋值 m = n
    else {
        auto * mov = new MoveInstruction(curF, left->val, right->val);
        node->blockInsts.addInst(mov);
        node->val = mov;
    }

    return true;
}

/// @brief return节点翻译成线性中间IR
/// @param node AST节点
/// @return 翻译是否成功，true：成功，false：失败
bool IRGenerator::ir_return(ast_node * node)
{
    Function * curFunc = module->getCurrentFunction();

    ast_node * exprNode = nullptr;
    if (!node->sons.empty()) {
        exprNode = ir_visit_ast_node(node->sons[0]);
        if (!exprNode)
            return false;
        // 把表达式对应的 IR 串到 blockInsts 里
        node->blockInsts.addInst(exprNode->blockInsts);
    }

    // 获取函数尾 Label 和返回值变量
    Instruction * exitLabel = curFunc->getExitLabel();
    LocalVariable * retVar = curFunc->getReturnValue();

    if (!exprNode) {
        // 无表达式 return，直接跳转到 exit
        node->blockInsts.addInst(new GotoInstruction(curFunc, exitLabel));
        node->val = nullptr;
        return true;
    }

    // 如果表达式本身就是一个 CmpInstruction，直接拿它的结果 (i1)
    Instruction * cmpInst = dynamic_cast<Instruction *>(exprNode->val);
    if (cmpInst && cmpInst->getOp() >= IRInstOperator::IRINST_OP_CMP_LT &&
        cmpInst->getOp() <= IRInstOperator::IRINST_OP_CMP_NE) {
        // 新建两个不中断的标签
        LabelInstruction * trueLabel = new LabelInstruction(curFunc);
        LabelInstruction * falseLabel = new LabelInstruction(curFunc);

        // 条件分支
        auto bcOp = mapCmpToBc(cmpInst->getOp());
        auto bcInst = new BranchCondInstruction(curFunc, bcOp, cmpInst, trueLabel, falseLabel);

        // 插入 bc
        node->blockInsts.addInst(bcInst);

        // trueLabel: returnValue = 1; goto exit
        node->blockInsts.addInst(trueLabel);
        node->blockInsts.addInst(new MoveInstruction(curFunc, retVar, module->newConstInt(1)));
        node->blockInsts.addInst(new GotoInstruction(curFunc, exitLabel));

        // falseLabel: returnValue = 0; goto exit
        node->blockInsts.addInst(falseLabel);
        node->blockInsts.addInst(new MoveInstruction(curFunc, retVar, module->newConstInt(0)));
        node->blockInsts.addInst(new GotoInstruction(curFunc, exitLabel));

        node->val = nullptr;
        return true;
    }

    // 否则就是普通表达式，按原来流程：move + goto
    node->blockInsts.addInst(new MoveInstruction(curFunc, retVar, exprNode->val));
    node->blockInsts.addInst(new GotoInstruction(curFunc, exitLabel));
    node->val = exprNode->val;
    return true;
}

/// @brief 类型叶子节点翻译成线性中间IR
/// @param node AST节点
/// @return 翻译是否成功，true：成功，false：失败
bool IRGenerator::ir_leaf_node_type(ast_node * node)
{
    // 不需要做什么，直接从节点中获取即可。

    return true;
}

/// @brief 标识符叶子节点翻译成线性中间IR，变量声明的不走这个语句
/// @param node AST节点
/// @return 翻译是否成功，true：成功，false：失败
bool IRGenerator::ir_leaf_node_var_id(ast_node * node)
{
    Value * val;

    // 查找ID型Value
    // 变量，则需要在符号表中查找对应的值

    val = module->findVarValue(node->name);

    node->val = val;

    return true;
}

/// @brief 无符号整数字面量叶子节点翻译成线性中间IR
/// @param node AST节点
/// @return 翻译是否成功，true：成功，false：失败
bool IRGenerator::ir_leaf_node_uint(ast_node * node)
{
    ConstInt * val;

    // 新建一个整数常量Value
    val = module->newConstInt((int32_t) node->integer_val);

    node->val = val;

    return true;
}

/// @brief 变量声明语句节点翻译成线性中间IR
/// @param node AST节点
/// @return 翻译是否成功，true：成功，false：失败
bool IRGenerator::ir_declare_statment(ast_node * node)
{
    bool result = true;
    for (auto * child: node->sons) {
        // child 是 AST_OP_VAR_DECL
        result = ir_variable_declare(child);
        if (!result)
            break;
        // 串联每个声明的 IR
        node->blockInsts.addInst(child->blockInsts);
    }
    return result;
}

/// @brief 变量定声明节点翻译成线性中间IR
/// @param node AST节点
/// @return 翻译是否成功，true：成功，false：失败
bool IRGenerator::ir_variable_declare(ast_node * node)
{
    Function * curF = module->getCurrentFunction();

    // node->sons[0] 是类型节点 type_node（基本类型或数组类型），
    // node->sons[1] 是 AST_OP_VAR_DEF
    Type * baseTy = node->sons[0]->type;
    ast_node * varDefNode = node->sons[1];

    // 先拿名字
    std::string varName = varDefNode->sons[0]->name;

    // 判断 varDefNode 是否有 dimsNode
    ast_node * dimsNode = nullptr;
    if (varDefNode->sons.size() >= 2 && varDefNode->sons[1] &&
        varDefNode->sons[1]->node_type == ast_operator_type::AST_OP_ARRAY_DIM) {
        dimsNode = varDefNode->sons[1];
    }

    Value * varValue = nullptr;
    if (dimsNode) {
        // 提取 dimsNode→vector<int>
        std::vector<int> dims;
        for (auto * eachDim: dimsNode->sons) {
            dims.push_back(static_cast<int>(eachDim->integer_val));
        }
        // 如果前端的 baseTy 是 i32，就得到 ArrayType(i32,{…})
        ArrayType * arrTy = Type::getArray(baseTy, dims);
        //自动判断是全局数组还是局部数组
        varValue = module->newVarValue(arrTy, varName);
    } else {
        // 普通标量
        varValue = module->newVarValue(baseTy, varName);
    }

    node->val = varValue;

    // 如果有初始值表达式，则插入赋值指令
    ast_node * initExpr = nullptr;
    if (varDefNode->sons.size() >= 2 && varDefNode->sons[1]->node_type != ast_operator_type::AST_OP_ARRAY_DIM) {
        initExpr = varDefNode->sons[1];
    }
    if (initExpr) {
        ast_node * exprNode = ir_visit_ast_node(initExpr);
        node->blockInsts.addInst(exprNode->blockInsts);
        if (!dimsNode) {
            // 标量：MoveInstruction
            node->blockInsts.addInst(new MoveInstruction(curF, static_cast<LocalVariable *>(varValue), exprNode->val));
        }
        // 判断是否为全局变量
        auto * globalVar = dynamic_cast<GlobalVariable *>(node->val);
        if (globalVar) {
            // 是全局变量，初值必须是常量
            Value * initVal = exprNode->val;
            // 检查是否是常量
            if (auto * constInt = dynamic_cast<ConstInt *>(initVal)) {
                globalVar->setInitValue(constInt);
            } else if (exprNode->node_type == ast_operator_type::AST_OP_SUB) {
                // 特殊处理负数：直接生成负常量
                int val = (int) exprNode->sons[1]->integer_val;
                globalVar->setInitValue(module->newConstInt(-val));
            }
            node->val = globalVar;
            return true;
        }
    }

    // else {
    //     // 默认初始化为 0
    //     if (!dimsNode) {
    //         // 标量：MoveInstruction
    //         node->blockInsts.addInst(
    //             new MoveInstruction(curF, static_cast<LocalVariable *>(varValue), module->newConstInt(0)));
    //     }
    //     // 数组整体初始化暂不支持
    // }

    return true;
}

/// @brief 比较节点翻译成线性中间IR
/// @param node AST节点
/// @return 翻译是否成功，true：成功，false：失败
bool IRGenerator::ir_cmp(ast_node * node)
{
    // sons[0]: 左表达式，sons[1]: 右表达式
    ast_node * left = ir_visit_ast_node(node->sons[0]);
    ast_node * right = ir_visit_ast_node(node->sons[1]);
    if (!left || !right || !left->val || !right->val)
        return false;

    // 选择 IRInstOperator
    IRInstOperator opc;
    switch (node->node_type) {
        case ast_operator_type::AST_OP_CMP_LT:
            opc = IRInstOperator::IRINST_OP_CMP_LT;
            break;
        case ast_operator_type::AST_OP_CMP_GT:
            opc = IRInstOperator::IRINST_OP_CMP_GT;
            break;
        case ast_operator_type::AST_OP_CMP_LE:
            opc = IRInstOperator::IRINST_OP_CMP_LE;
            break;
        case ast_operator_type::AST_OP_CMP_GE:
            opc = IRInstOperator::IRINST_OP_CMP_GE;
            break;
        case ast_operator_type::AST_OP_CMP_EQ:
            opc = IRInstOperator::IRINST_OP_CMP_EQ;
            break;
        case ast_operator_type::AST_OP_CMP_NE:
            opc = IRInstOperator::IRINST_OP_CMP_NE;
            break;
        default:
            return false;
    }

    // 生成 cmp 指令
    auto cmpInst =
        new CmpInstruction(module->getCurrentFunction(), opc, left->val, right->val, IntegerType::getTypeBool());

    // 串联 IR
    // 先插入 left 的 IR
    node->blockInsts.addInst(left->blockInsts);
    // 再插入 right 的 IR
    node->blockInsts.addInst(right->blockInsts);
    // 最后插入 cmpInst
    node->blockInsts.addInst(cmpInst);

    node->val = cmpInst;
    return true;
}

/// @brief 有条件跳转节点翻译成线性中间IR
/// @param node AST节点
/// @return 翻译是否成功，true：成功，false：失败
bool IRGenerator::ir_bc(ast_node * node)
{
    // sons[0]=cmpNode, sons[1]=真label AST, sons[2]=假label AST
    ast_node * cmpNode = ir_visit_ast_node(node->sons[0]);
    if (!cmpNode)
        return false;

    // 从第二、三孩子提取 LabelInstruction*
    LabelInstruction * trueLabel = static_cast<LabelInstruction *>(node->sons[1]->val);
    LabelInstruction * falseLabel = static_cast<LabelInstruction *>(node->sons[2]->val);
    if (!trueLabel || !falseLabel)
        return false;

    Instruction * cmpInst = static_cast<Instruction *>(cmpNode->val);
    auto bcOp = mapCmpToBc(cmpInst->getOp());
    // 生成条件跳转
    auto bcInst = new BranchCondInstruction(module->getCurrentFunction(), bcOp, cmpNode->val, trueLabel, falseLabel);

    node->blockInsts.addInst(cmpNode->blockInsts);
    node->blockInsts.addInst(bcInst);

    return true;
}

bool IRGenerator::ir_if(ast_node * node)
{
    // sons[0] = 条件 AST
    // sons[1] = 真分支 AST
    // sons[2] = 假分支 AST （可能为 nullptr）

    Function * curF = module->getCurrentFunction();

    auto L_true = new LabelInstruction(curF);
    auto L_false = new LabelInstruction(curF);
    auto L_end = new LabelInstruction(curF);

    // 使用控制流方式翻译布尔表达式
    if (!ir_translate_bool_expr(node->sons[0], L_true, L_false))
        return false;
    node->blockInsts.addInst(node->sons[0]->blockInsts);

    // 真分支
    node->blockInsts.addInst(L_true);
    if (!ir_visit_ast_node(node->sons[1]))
        return false;
    node->blockInsts.addInst(node->sons[1]->blockInsts);
    node->blockInsts.addInst(new GotoInstruction(curF, L_end));

    // 假分支
    node->blockInsts.addInst(L_false);
    if (node->sons.size() > 2 && node->sons[2]) {
        if (!ir_visit_ast_node(node->sons[2]))
            return false;
        node->blockInsts.addInst(node->sons[2]->blockInsts);
    }
    node->blockInsts.addInst(new GotoInstruction(curF, L_end));

    // 结束
    node->blockInsts.addInst(L_end);
    node->val = nullptr;
    return true;
}

/// @brief 逻辑与短路式翻译
/// @param node AST 节点 (AST_OP_AND)
/// @return 翻译是否完成
bool IRGenerator::ir_and(ast_node * node)
{
    // 获取当前正在处理的函数对象
    Function * curF = module->getCurrentFunction();

    // 创建逻辑与运算所需的三个标签指令
    // 短路求值的逻辑与运算需要根据左操作数的值来决定是否计算右操作数
    // 执行流程：如果左操作数为假，直接跳转到L_false；如果左操作数为真，继续计算右操作数
    auto L_true = new LabelInstruction(curF);   // 真值标签：当整个表达式结果为真时跳转到此处
    auto L_false = new LabelInstruction(curF);  // 假值标签：当整个表达式结果为假时跳转到此处
    auto L_end = new LabelInstruction(curF);    // 结束标签：逻辑运算完成后的汇合点

    // 创建一个临时变量来存储逻辑与运算的最终结果
    // 逻辑运算的结果是布尔值，在IR中用整数表示：1表示真，0表示假
    auto res = module->newVarValue(IntegerType::getTypeInt());
    node->val = res;  // 将结果变量关联到当前AST节点

    // 调用ir_logical_and函数生成短路求值的条件跳转逻辑
    // 该函数会递归处理左右操作数，并生成相应的条件跳转指令：
    // - 对于 a && b：如果a为假，直接跳转到L_false；如果a为真，继续计算b
    // - 如果b也为真，跳转到L_true；如果b为假，跳转到L_false
    if (!ir_logical_and(node, L_true, L_false))
        return false;  // 短路逻辑生成失败

    // 处理真值分支：当整个逻辑与表达式结果为真时执行
    // 这种情况发生在所有操作数都为真的时候
    node->blockInsts.addInst(L_true);  // 放置真值标签
    // 将结果变量设置为1（表示真值）
    node->blockInsts.addInst(new MoveInstruction(curF, res, module->newConstInt(1)));
    // 跳转到结束标签，避免执行假值分支的代码
    node->blockInsts.addInst(new GotoInstruction(curF, L_end));

    // 处理假值分支：当整个逻辑与表达式结果为假时执行
    // 这种情况发生在任何一个操作数为假的时候（短路求值）
    node->blockInsts.addInst(L_false);  // 放置假值标签
    // 将结果变量设置为0（表示假值）
    node->blockInsts.addInst(new MoveInstruction(curF, res, module->newConstInt(0)));
    // 跳转到结束标签（虽然这里是最后一个分支，goto可以省略，但为了代码一致性保留）
    node->blockInsts.addInst(new GotoInstruction(curF, L_end));

    // 放置结束标签，作为所有分支的汇合点
    // 无论表达式结果为真还是假，最终都会到达这里继续执行后续代码
    node->blockInsts.addInst(L_end);
    return true;  // 成功生成逻辑与运算的IR代码
}

/// @brief 逻辑或短路式翻译
/// @param node AST 节点 (AST_OP_OR)
/// @return 翻译是否完成
bool IRGenerator::ir_or(ast_node * node)
{
    // 获取当前正在处理的函数对象
    Function * curF = module->getCurrentFunction();

    // 创建逻辑或运算所需的三个标签指令
    // 短路求值的逻辑或运算需要根据左操作数的值来决定是否计算右操作数
    // 执行流程：如果左操作数为真，直接跳转到L_true；如果左操作数为假，继续计算右操作数
    auto L_true = new LabelInstruction(curF);   // 真值标签：当整个表达式结果为真时跳转到此处
    auto L_false = new LabelInstruction(curF);  // 假值标签：当整个表达式结果为假时跳转到此处
    auto L_end = new LabelInstruction(curF);    // 结束标签：逻辑运算完成后的汇合点

    // 创建一个临时变量来存储逻辑或运算的最终结果
    // 逻辑运算的结果是布尔值，在IR中用整数表示：1表示真，0表示假
    auto res = module->newVarValue(IntegerType::getTypeInt());
    node->val = res;  // 将结果变量关联到当前AST节点

    // 调用ir_logical_or函数生成短路求值的条件跳转逻辑
    // 该函数会递归处理左右操作数，并生成相应的条件跳转指令：
    // - 对于 a || b：如果a为真，直接跳转到L_true；如果a为假，继续计算b
    // - 如果b为真，跳转到L_true；如果b也为假，跳转到L_false
    if (!ir_logical_or(node, L_true, L_false))
        return false;  // 短路逻辑生成失败

    // 处理真值分支：当整个逻辑或表达式结果为真时执行
    // 这种情况发生在任何一个操作数为真的时候（短路求值）
    node->blockInsts.addInst(L_true);  // 放置真值标签
    // 将结果变量设置为1（表示真值）
    node->blockInsts.addInst(new MoveInstruction(curF, res, module->newConstInt(1)));
    // 跳转到结束标签，避免执行假值分支的代码
    node->blockInsts.addInst(new GotoInstruction(curF, L_end));

    // 处理假值分支：当整个逻辑或表达式结果为假时执行
    // 这种情况发生在所有操作数都为假的时候
    node->blockInsts.addInst(L_false);  // 放置假值标签
    // 将结果变量设置为0（表示假值）
    node->blockInsts.addInst(new MoveInstruction(curF, res, module->newConstInt(0)));
    // 跳转到结束标签（虽然这里是最后一个分支，goto可以省略，但为了代码一致性保留）
    node->blockInsts.addInst(new GotoInstruction(curF, L_end));

    // 放置结束标签，作为所有分支的汇合点
    // 无论表达式结果为真还是假，最终都会到达这里继续执行后续代码
    node->blockInsts.addInst(L_end);
    return true;  // 成功生成逻辑或运算的IR代码
}

/// @brief 逻辑非翻译
/// @param node AST 节点 (AST_OP_NOT)
/// @return 翻译是否完成
bool IRGenerator::ir_not(ast_node * node)
{
    Function * curF = module->getCurrentFunction();
    auto L_true = new LabelInstruction(curF);
    auto L_false = new LabelInstruction(curF);
    auto L_end = new LabelInstruction(curF);
    auto res = module->newVarValue(IntegerType::getTypeInt());
    node->val = res;

    if (!ir_logical_not(node, L_true, L_false))
        return false;

    node->blockInsts.addInst(L_true);
    node->blockInsts.addInst(new MoveInstruction(curF, res, module->newConstInt(1)));
    node->blockInsts.addInst(new GotoInstruction(curF, L_end));

    node->blockInsts.addInst(L_false);
    node->blockInsts.addInst(new MoveInstruction(curF, res, module->newConstInt(0)));
    node->blockInsts.addInst(new GotoInstruction(curF, L_end));

    node->blockInsts.addInst(L_end);
    return true;
}

bool IRGenerator::ir_translate_bool_expr(ast_node * node, LabelInstruction * L_true, LabelInstruction * L_false)
{
    if (!node) {
        // 条件表达式为空，直接跳转到真出口
        node->blockInsts.addInst(new GotoInstruction(module->getCurrentFunction(), L_true));
        return true;
    }

    // 检查是否是常量表达式
    if (node->node_type == ast_operator_type::AST_OP_LEAF_LITERAL_UINT) {
        if (node->integer_val == 0) {
            // 常量 0，直接跳转到假出口
            node->blockInsts.addInst(new GotoInstruction(module->getCurrentFunction(), L_false));
            return true;
        } else {
            // 其它常量，直接跳转到真出口
            node->blockInsts.addInst(new GotoInstruction(module->getCurrentFunction(), L_true));
            return true;
        }
    }

    // 处理逻辑运算和关系表达式
    switch (node->node_type) {
        case ast_operator_type::AST_OP_AND:
            return ir_logical_and(node, L_true, L_false);
        case ast_operator_type::AST_OP_OR:
            return ir_logical_or(node, L_true, L_false);
        case ast_operator_type::AST_OP_NOT:
            return ir_logical_not(node, L_true, L_false);
        default: {
            // 普通关系表达式，生成 cond br
            Function * curF = module->getCurrentFunction();
            ast_node * condNode = ir_visit_ast_node(node);
            if (!condNode)
                return false;

            IRInstOperator bcOp = IRInstOperator::IRINST_OP_BC;
            if (auto * cmpInst = dynamic_cast<Instruction *>(condNode->val)) {
                bcOp = mapCmpToBc(cmpInst->getOp());
            }

            auto bc = new BranchCondInstruction(curF, bcOp, condNode->val, L_true, L_false);
            node->blockInsts.addInst(bc);
            return true;
        }
    }
}

Value * IRGenerator::ir_eval_bool_expr(ast_node * node)
{
    auto curF = module->getCurrentFunction();
    auto L_true = new LabelInstruction(curF);
    auto L_false = new LabelInstruction(curF);
    auto L_end = new LabelInstruction(curF);
    auto res = module->newVarValue(IntegerType::getTypeInt());
    node->val = res;

    if (!ir_translate_bool_expr(node, L_true, L_false))
        return nullptr;

    node->blockInsts.addInst(L_true);
    node->blockInsts.addInst(new MoveInstruction(curF, res, module->newConstInt(1)));
    node->blockInsts.addInst(new GotoInstruction(curF, L_end));

    node->blockInsts.addInst(L_false);
    node->blockInsts.addInst(new MoveInstruction(curF, res, module->newConstInt(0)));
    node->blockInsts.addInst(new GotoInstruction(curF, L_end));

    node->blockInsts.addInst(L_end);
    return res;
}

bool IRGenerator::ir_logical_and(ast_node * node, LabelInstruction * L_true, LabelInstruction * L_false)
{
    Function * curF = module->getCurrentFunction();

    // 创建中间标签 L_mid，表示左边为真时跳转
    auto L_mid = new LabelInstruction(curF);

    // sons[0] && sons[1]
    auto left = node->sons[0];
    auto right = node->sons[1];

    // 先翻译左边，真出口为 L_mid，假出口为 L_false
    if (!ir_translate_bool_expr(left, L_mid, L_false))
        return false;
    node->blockInsts.addInst(left->blockInsts);

    // 插入 L_mid
    node->blockInsts.addInst(L_mid);

    // 再翻译右边，真出口为 L_true，假出口为 L_false
    if (!ir_translate_bool_expr(right, L_true, L_false))
        return false;
    node->blockInsts.addInst(right->blockInsts);

    return true;
}

bool IRGenerator::ir_logical_or(ast_node * node, LabelInstruction * L_true, LabelInstruction * L_false)
{
    Function * curF = module->getCurrentFunction();

    // 创建中间标签 L_mid，表示左边为假时跳转
    auto L_mid = new LabelInstruction(curF);

    auto left = node->sons[0];
    auto right = node->sons[1];

    // 左边：真出口为 L_true，假出口为 L_mid
    if (!ir_translate_bool_expr(left, L_true, L_mid))
        return false;
    node->blockInsts.addInst(left->blockInsts);

    // 插入 L_mid
    node->blockInsts.addInst(L_mid);

    // 右边：真出口为 L_true，假出口为 L_false
    if (!ir_translate_bool_expr(right, L_true, L_false))
        return false;
    node->blockInsts.addInst(right->blockInsts);

    return true;
}

bool IRGenerator::ir_logical_not(ast_node * node, LabelInstruction * L_true, LabelInstruction * L_false)
{
    auto inner = node->sons[0];

    // 非操作只需调换真假出口
    if (!ir_translate_bool_expr(inner, L_false, L_true))
        return false;
    node->blockInsts.addInst(inner->blockInsts);

    return true;
}

bool IRGenerator::ir_while(ast_node * node)
{
    // 获取当前正在处理的函数对象
    Function * curF = module->getCurrentFunction();

    // 创建while循环所需的三个标签指令
    // L_cond: 条件判断标签，用于跳转到条件测试代码
    // L_body: 循环体标签，条件为真时跳转到此处执行循环体
    // L_end: 循环结束标签，条件为假或遇到break时跳转到此处
    auto L_cond = new LabelInstruction(curF);
    auto L_body = new LabelInstruction(curF);
    auto L_end = new LabelInstruction(curF);

    // 1) 将当前循环的条件标签和结束标签压入栈中
    // 这样做是为了支持嵌套循环，内层循环的break/continue能正确找到对应的标签
    // loopCondStack用于continue语句，loopEndStack用于break语句
    loopCondStack.push_back(L_cond);
    loopEndStack.push_back(L_end);

    // 2) 生成无条件跳转指令，直接跳到条件测试部分
    // 这样确保进入循环时先进行条件判断
    node->blockInsts.addInst(new GotoInstruction(curF, L_cond));

    // 3) 处理循环体部分
    // 首先放置循环体标签，作为条件为真时的跳转目标
    node->blockInsts.addInst(L_body);
    // 递归处理循环体AST节点（node->sons[1]是循环体子节点）
    ast_node * bodyNode = ir_visit_ast_node(node->sons[1]);
    if (!bodyNode)
        return false;  // 循环体处理失败，返回错误
    // 将循环体生成的指令添加到当前节点的指令块中
    node->blockInsts.addInst(bodyNode->blockInsts);

    // 4) 处理条件测试部分
    // 放置条件测试标签，作为循环开始和continue的跳转目标
    node->blockInsts.addInst(L_cond);
    // 获取条件表达式AST节点（node->sons[0]是条件表达式子节点）
    ast_node * condAst = node->sons[0];
    // 将条件表达式翻译为布尔跳转指令
    // 条件为真时跳转到L_body（继续循环），条件为假时跳转到L_end（退出循环）
    if (!ir_translate_bool_expr(condAst, L_body, L_end))
        return false;  // 条件表达式处理失败，返回错误
    // 将条件表达式生成的指令添加到当前节点的指令块中
    node->blockInsts.addInst(condAst->blockInsts);

    // 5) 放置循环结束标签
    // 作为循环正常结束或break语句的跳转目标
    node->blockInsts.addInst(L_end);

    // 6) 清理循环标签栈
    // 当前循环处理完毕，将对应的标签从栈中弹出
    // 这样外层循环的break/continue就能正确找到对应的标签
    loopCondStack.pop_back();
    loopEndStack.pop_back();

    // while语句本身不产生值，设置为nullptr
    node->val = nullptr;
    return true;  // 成功处理while循环
}

bool IRGenerator::ir_break(ast_node * node)
{
    // 获取当前正在处理的函数对象，用于创建跳转指令
    Function * curF = module->getCurrentFunction();

    // 检查当前是否在循环内部
    // loopEndStack栈存储了所有嵌套循环的结束标签
    // 如果栈为空，说明当前不在任何循环内部，break语句是非法的
    if (loopEndStack.empty()) {
        // 错误：break语句出现在循环外部，这是语法错误
        return false;
    }

    // 生成无条件跳转指令，跳转到最近的循环结束标签
    // loopEndStack.back()获取栈顶元素，即最内层循环的结束标签
    // 这样可以正确处理嵌套循环的情况，break总是跳出最内层循环
    node->blockInsts.addInst(new GotoInstruction(curF, loopEndStack.back()));

    // break语句本身不产生值，设置为nullptr
    node->val = nullptr;
    return true;  // 成功处理break语句
}

bool IRGenerator::ir_continue(ast_node * node)
{
    // 获取当前正在处理的函数对象，用于创建跳转指令
    Function * curF = module->getCurrentFunction();

    // 检查当前是否在循环内部
    // loopCondStack栈存储了所有嵌套循环的条件测试标签
    // 如果栈为空，说明当前不在任何循环内部，continue语句是非法的
    if (loopCondStack.empty()) {
        // 错误：continue语句出现在循环外部，这是语法错误
        return false;
    }

    // 生成无条件跳转指令，跳转到最近的循环条件测试标签
    // loopCondStack.back()获取栈顶元素，即最内层循环的条件测试标签
    // 这样可以正确处理嵌套循环的情况，continue总是跳到最内层循环的条件判断处
    // 跳转到条件测试标签后，会重新进行条件判断，决定是否继续执行循环体
    node->blockInsts.addInst(new GotoInstruction(curF, loopCondStack.back()));

    // continue语句本身不产生值，设置为nullptr
    node->val = nullptr;
    return true;  // 成功处理continue语句
}

/// @brief 计算数组地址
bool IRGenerator::ir_array_access(ast_node * node)
{
    Function * current_function = module->getCurrentFunction();

    // Part 1: Evaluate and collect all index expressions.
    std::vector<Value *> index_values;
    ast_node * index_list_node = node->sons[1]; // The AST node for the index list.
    for (auto * index_ast: index_list_node->sons) {
        ast_node * processed_index_node = ir_visit_ast_node(index_ast);
        if (!processed_index_node) {
            return false;
        }
        node->blockInsts.addInst(processed_index_node->blockInsts);
        index_values.push_back(processed_index_node->val);
    }

    // Part 2: Retrieve array's base info (address, type, dimensions).
    std::string array_name = node->sons[0]->name;
    Value * array_base_ptr = module->findVarValue(array_name);
    auto * array_type = dynamic_cast<ArrayType *>(array_base_ptr->getType());
    if (!array_type) {
        assert(array_type && "Array access on a non-array type is not allowed.");
        return false;
    }
    const std::vector<int> & dimensions = array_type->getDimensions();

    // Part 3: Compute the flattened 1D offset from multidimensional indices.
    Value * element_offset = nullptr;
    if (!index_values.empty()) {
        element_offset = index_values[0];
        for (size_t i = 1; i < index_values.size(); ++i) {
            // Formula: offset = (prev_offset * current_dim_size) + current_index
            auto * mul_instr = new BinaryInstruction(current_function,
                                                   IRInstOperator::IRINST_OP_MUL_I,
                                                   element_offset,
                                                   module->newConstInt(dimensions[i]),
                                                   IntegerType::getTypeInt());
            node->blockInsts.addInst(mul_instr);

            auto * add_instr = new BinaryInstruction(current_function,
                                                   IRInstOperator::IRINST_OP_ADD_I,
                                                   mul_instr,
                                                   index_values[i],
                                                   IntegerType::getTypeInt());
            node->blockInsts.addInst(add_instr);
            element_offset = add_instr;
        }
    }

    // For partial access (e.g., a[i] for a[i][j]), scale offset by size of remaining dimensions.
    if (index_values.size() < dimensions.size()) {
        int tail_dims_product = 1;
        for (size_t j = index_values.size(); j < dimensions.size(); ++j) {
            tail_dims_product *= dimensions[j];
        }

        auto * scale_instr = new BinaryInstruction(current_function,
                                               IRInstOperator::IRINST_OP_MUL_I,
                                               element_offset,
                                               module->newConstInt(tail_dims_product),
                                               IntegerType::getTypeInt());
        node->blockInsts.addInst(scale_instr);
        element_offset = scale_instr;
    }

    // Part 4: Convert element offset to byte offset (assuming 4-byte elements).
    auto * byte_offset = new BinaryInstruction(current_function,
                                            IRInstOperator::IRINST_OP_MUL_I,
                                            element_offset,
                                            module->newConstInt(4),
                                            IntegerType::getTypeInt());
    node->blockInsts.addInst(byte_offset);

    // Part 5: Compute the final address by adding byte offset to base pointer.
    Type * element_ptr_type = PointerType::get(array_type->getElementType());
    auto * final_address =
        new BinaryInstruction(current_function, IRInstOperator::IRINST_OP_ADD_I, array_base_ptr, byte_offset, element_ptr_type);
    node->blockInsts.addInst(final_address);

    // Part 6: If access is complete, load value. Otherwise, return the sub-array pointer.
    if (index_values.size() == dimensions.size()) {
        // Full access: load the value at the final address.
        auto * ptr_type = static_cast<PointerType *>(final_address->getType());
        Type * value_type = const_cast<Type *>(ptr_type->getPointeeType());
        auto * load_instr = new LoadInstruction(current_function, final_address, value_type);
        node->blockInsts.addInst(load_instr);

        node->ptrval = final_address; // Keep address for assignments (e.g., a[i]=1).
        node->val = load_instr;       // Result of the expression is the loaded value.
    } else {
        // Partial access: result is a pointer to the sub-array.
        std::vector<int> remaining_dims(dimensions.begin() + index_values.size(), dimensions.end());
        ArrayType * sub_array_type = Type::getArray(array_type->getElementType(), remaining_dims);

        // Update the pointer's type to reflect it points to a sub-array.
        final_address->setType(sub_array_type);
        node->val = final_address;
    }

    return true;
}
bool IRGenerator::ir_for(ast_node * node)
{
    Function * curF = module->getCurrentFunction();

    // 创建标签
    auto L_cond = new LabelInstruction(curF); // 条件测试标签
    auto L_body = new LabelInstruction(curF); // 循环体标签
    auto L_step = new LabelInstruction(curF); // 步进标签
    auto L_end = new LabelInstruction(curF);  // 循环结束标签

    // 将标签压入栈中，便于处理 break 和 continue
    // 注意：如果步进表达式为空，continue应该跳转到条件测试；否则跳转到步进表达式
    loopCondStack.push_back(node->sons[2]->node_type == ast_operator_type::AST_OP_EMPTY ? L_cond : L_step);
    loopEndStack.push_back(L_end);

    // 进入 for 的局部作用域，使for循环内部定义的变量只在循环内可见
    module->enterScope();
    
    // 1. 初值表达式 - 对应for(初值; 条件; 步进)中的"初值"部分
    // 只有当初值表达式存在且不为空节点时才处理
    if (node->sons[0] && node->sons[0]->node_type != ast_operator_type::AST_OP_EMPTY) {
        ast_node * initNode = ir_visit_ast_node(node->sons[0]);
        if (!initNode)
            return false;
        // 将初值表达式的IR指令添加到当前节点的指令列表中
        node->blockInsts.addInst(initNode->blockInsts);
    }

    // 2. 跳转到条件测试标签 - for循环的执行流程是先初始化，然后测试条件
    node->blockInsts.addInst(new GotoInstruction(curF, L_cond));

    // 3. 条件测试 - 对应for(初值; 条件; 步进)中的"条件"部分
    node->blockInsts.addInst(L_cond);
    if (node->sons[1] && node->sons[1]->node_type != ast_operator_type::AST_OP_EMPTY) {
        // 条件表达式存在且不为空
        ast_node * condNode = ir_visit_ast_node(node->sons[1]);
        if (!condNode)
            return false;

        // 使用条件跳转指令：条件为真跳转到循环体，条件为假跳转到循环结束
        if (!ir_translate_bool_expr(condNode, L_body, L_end))
            return false;
        node->blockInsts.addInst(condNode->blockInsts);
    } else {
        // 如果条件表达式为空，相当于while(true)，直接跳转到循环体
        node->blockInsts.addInst(new GotoInstruction(curF, L_body));
    }

    // 4. 循环体 - 对应for循环中的执行语句块
    node->blockInsts.addInst(L_body);
    if (node->sons[3]) {
        // 循环体存在
        ast_node * bodyNode = ir_visit_ast_node(node->sons[3]);
        if (!bodyNode)
            return false;
        node->blockInsts.addInst(bodyNode->blockInsts);
    } else {
        // 循环体为空，直接跳转到步进标签
        node->blockInsts.addInst(new GotoInstruction(curF, L_step));
    }

    // 循环体执行完毕后，无条件跳转到步进表达式
    node->blockInsts.addInst(new GotoInstruction(curF, L_step));

    // 5. 步进表达式 - 对应for(初值; 条件; 步进)中的"步进"部分
    node->blockInsts.addInst(L_step);
    if (node->sons[2] && node->sons[2]->node_type != ast_operator_type::AST_OP_EMPTY) {
        // 步进表达式存在且不为空
        ast_node * stepNode = ir_visit_ast_node(node->sons[2]);
        if (!stepNode)
            return false;
        node->blockInsts.addInst(stepNode->blockInsts);
    }

    // 步进表达式执行完毕后，无条件跳转回条件测试，形成循环
    node->blockInsts.addInst(new GotoInstruction(curF, L_cond));

    // 6. 循环结束标签 - 条件为假或执行break时跳转到这里
    node->blockInsts.addInst(L_end);

    // 弹出当前循环标签，恢复外层循环的break/continue目标
    loopCondStack.pop_back();
    loopEndStack.pop_back();

    // 离开 for 的局部作用域，使for循环内部定义的变量失效
    module->leaveScope();
    node->val = nullptr; // for语句没有返回值
    return true;
}

bool IRGenerator::ir_pre_inc_dec(ast_node * node)
{
    // 前置自增/自减运算符 (++i 或 --i)
    Function * curF = module->getCurrentFunction();

    // 获取操作数（变量）- 即被自增/自减的变量
    ast_node * operandNode = node->sons[0];
    ast_node * operand = ir_visit_ast_node(operandNode);
    if (!operand)
        return false;

    // 根据节点类型确定是自增还是自减操作
    IRInstOperator op = (node->node_type == ast_operator_type::AST_OP_PRE_INC) ? IRInstOperator::IRINST_OP_ADD_I
                                                                               : IRInstOperator::IRINST_OP_SUB_I;

    // 生成加法或减法指令 - 计算 operand + 1 或 operand - 1
    auto * tempValue = module->newVarValue(IntegerType::getTypeInt());
    auto * incDecInst =
        new BinaryInstruction(curF, op, operand->val, module->newConstInt(1), IntegerType::getTypeInt());
    node->blockInsts.addInst(incDecInst);

    // 更新变量值 - 将计算结果存回原变量
    auto * movInst = new MoveInstruction(curF, operand->val, incDecInst);
    node->blockInsts.addInst(movInst);

    // 返回更新后的值 - 前置自增/自减的结果是更新后的值
    auto * returnInst = new MoveInstruction(curF, tempValue, operand->val);
    node->blockInsts.addInst(returnInst);

    node->val = tempValue; // 设置表达式的值为更新后的变量值
    return true;
}

bool IRGenerator::ir_post_inc_dec(ast_node * node)
{
    // 后置自增/自减运算符 (i++ 或 i--)
    Function * curF = module->getCurrentFunction();

    // 获取操作数（变量）- 即被自增/自减的变量
    ast_node * operandNode = node->sons[0];
    ast_node * operand = ir_visit_ast_node(operandNode);
    if (!operand)
        return false;

    // 保存原始值 - 后置运算符需要返回运算前的值
    auto * originalValue = module->newVarValue(IntegerType::getTypeInt());
    auto * saveInst = new MoveInstruction(curF, originalValue, operand->val);
    node->blockInsts.addInst(saveInst);

    // 根据节点类型确定是自增还是自减操作
    IRInstOperator op = (node->node_type == ast_operator_type::AST_OP_POST_INC) ? IRInstOperator::IRINST_OP_ADD_I
                                                                                : IRInstOperator::IRINST_OP_SUB_I;

    // 生成加法或减法指令 - 计算 operand + 1 或 operand - 1
    auto * tempValue = module->newVarValue(IntegerType::getTypeInt());
    auto * incDecInst =
        new BinaryInstruction(curF, op, operand->val, module->newConstInt(1), IntegerType::getTypeInt());
    node->blockInsts.addInst(incDecInst);

    // 更新变量值 - 将计算结果存回原变量
    auto * movInst = new MoveInstruction(curF, operand->val, incDecInst);
    node->blockInsts.addInst(movInst);

    // 返回原始值 - 后置自增/自减的结果是更新前的值
    auto * returnInst = new MoveInstruction(curF, tempValue, originalValue);
    node->blockInsts.addInst(returnInst);

    node->val = tempValue; // 设置表达式的值为原始变量值
    return true;
}
