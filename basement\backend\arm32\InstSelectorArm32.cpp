﻿///
/// @file InstSelectorArm32.cpp
/// @brief 指令选择器-ARM32的实现
/// <AUTHOR> (<EMAIL>)
/// @version 1.0
/// @date 2024-11-21
///
/// @copyright Copyright (c) 2024
///
/// @par 修改日志:
/// <table>
/// <tr><th>Date       <th>Version <th>Author  <th>Description
/// <tr><td>2024-11-21 <td>1.0     <td>zenglj  <td>新做
/// </table>
///
#include <cstdio>

#include "Common.h"
#include "ILocArm32.h"
#include "InstSelectorArm32.h"
#include "PlatformArm32.h"

#include "PointerType.h"
#include "RegVariable.h"
#include "Function.h"

#include "LabelInstruction.h"
#include "GotoInstruction.h"
#include "FuncCallInstruction.h"
#include "MoveInstruction.h"
#include "BranchCondInstruction.h"
#include "CmpInstruction.h"

/// @brief 构造函数
/// @param _irCode 指令
/// @param _iloc ILoc
/// @param _func 函数
InstSelectorArm32::InstSelectorArm32(vector<Instruction *> & _irCode,
                                     ILocArm32 & _iloc,
                                     Function * _func,
                                     SimpleRegisterAllocator & allocator)
    : ir(_irCode), iloc(_iloc), func(_func), simpleRegisterAllocator(allocator)
{
    translator_handlers[IRInstOperator::IRINST_OP_ENTRY] = &InstSelectorArm32::translate_entry;
    translator_handlers[IRInstOperator::IRINST_OP_EXIT] = &InstSelectorArm32::translate_exit;

    translator_handlers[IRInstOperator::IRINST_OP_LABEL] = &InstSelectorArm32::translate_label;
    translator_handlers[IRInstOperator::IRINST_OP_GOTO] = &InstSelectorArm32::translate_goto;

    translator_handlers[IRInstOperator::IRINST_OP_ASSIGN] = &InstSelectorArm32::translate_assign;

    translator_handlers[IRInstOperator::IRINST_OP_ADD_I] = &InstSelectorArm32::translate_add_int32;
    translator_handlers[IRInstOperator::IRINST_OP_SUB_I] = &InstSelectorArm32::translate_sub_int32;
    translator_handlers[IRInstOperator::IRINST_OP_MUL_I] = &InstSelectorArm32::translate_mul_int32;
    translator_handlers[IRInstOperator::IRINST_OP_SDIV_I] = &InstSelectorArm32::translate_div_int32;
    translator_handlers[IRInstOperator::IRINST_OP_MOD_I] = &InstSelectorArm32::translate_mod_int32;

    translator_handlers[IRInstOperator::IRINST_OP_CMP_LT] = &InstSelectorArm32::translate_cmp_lt;
    translator_handlers[IRInstOperator::IRINST_OP_CMP_GT] = &InstSelectorArm32::translate_cmp_gt;
    translator_handlers[IRInstOperator::IRINST_OP_CMP_LE] = &InstSelectorArm32::translate_cmp_le;
    translator_handlers[IRInstOperator::IRINST_OP_CMP_GE] = &InstSelectorArm32::translate_cmp_ge;
    translator_handlers[IRInstOperator::IRINST_OP_CMP_EQ] = &InstSelectorArm32::translate_cmp_eq;
    translator_handlers[IRInstOperator::IRINST_OP_CMP_NE] = &InstSelectorArm32::translate_cmp_ne;
    translator_handlers[IRInstOperator::IRINST_OP_BC_LT] = &InstSelectorArm32::translate_bc;
    translator_handlers[IRInstOperator::IRINST_OP_BC_GT] = &InstSelectorArm32::translate_bc;
    translator_handlers[IRInstOperator::IRINST_OP_BC_LE] = &InstSelectorArm32::translate_bc;
    translator_handlers[IRInstOperator::IRINST_OP_BC_GE] = &InstSelectorArm32::translate_bc;
    translator_handlers[IRInstOperator::IRINST_OP_BC_EQ] = &InstSelectorArm32::translate_bc;
    translator_handlers[IRInstOperator::IRINST_OP_BC_NE] = &InstSelectorArm32::translate_bc;
    translator_handlers[IRInstOperator::IRINST_OP_BC] = &InstSelectorArm32::translate_bc;

    translator_handlers[IRInstOperator::IRINST_OP_FUNC_CALL] = &InstSelectorArm32::translate_call;
    translator_handlers[IRInstOperator::IRINST_OP_ARG] = &InstSelectorArm32::translate_arg;

    translator_handlers[IRInstOperator::IRINST_OP_LOAD] = &InstSelectorArm32::translate_load;
    translator_handlers[IRInstOperator::IRINST_OP_STORE] = &InstSelectorArm32::translate_store;
}

///
/// @brief 析构函数
///
InstSelectorArm32::~InstSelectorArm32()
{}

/// @brief 指令选择执行
void InstSelectorArm32::run()
{
    for (auto inst: ir) {

        // 逐个指令进行翻译
        if (!inst->isDead()) {
            translate(inst);
        }
    }
}

/// @brief 指令翻译成ARM32汇编
/// @param inst IR指令
void InstSelectorArm32::translate(Instruction * inst)
{
    // 操作符
    IRInstOperator op = inst->getOp();

    map<IRInstOperator, translate_handler>::const_iterator pIter;
    pIter = translator_handlers.find(op);
    if (pIter == translator_handlers.end()) {
        // 没有找到，则说明当前不支持
        printf("Translate: Operator(%d) not support", (int) op);
        return;
    }

    // 开启时输出IR指令作为注释
    if (showLinearIR) {
        outputIRInstruction(inst);
    }

    (this->*(pIter->second))(inst);
}

///
/// @brief 输出IR指令
///
void InstSelectorArm32::outputIRInstruction(Instruction * inst)
{
    std::string irStr;
    inst->toString(irStr);
    if (!irStr.empty()) {
        iloc.comment(irStr);
    }
}

/// @brief NOP翻译成ARM32汇编
/// @param inst IR指令
void InstSelectorArm32::translate_nop(Instruction * inst)
{
    (void) inst;
    iloc.nop();
}

/// @brief Label指令指令翻译成ARM32汇编
/// @param inst IR指令
void InstSelectorArm32::translate_label(Instruction * inst)
{
    Instanceof(labelInst, LabelInstruction *, inst);

    iloc.label(labelInst->getName());
}

/// @brief goto指令指令翻译成ARM32汇编
/// @param inst IR指令
void InstSelectorArm32::translate_goto(Instruction * inst)
{
    Instanceof(gotoInst, GotoInstruction *, inst);

    // 无条件跳转
    iloc.jump(gotoInst->getTarget()->getName());
}

/// @brief 函数入口指令翻译成ARM32汇编
/// @param inst IR指令
void InstSelectorArm32::translate_entry(Instruction * inst)
{
    // 查看保护的寄存器
    auto & protectedRegNo = func->getProtectedReg();
    auto & protectedRegStr = func->getProtectedRegStr();

    bool first = true;
    for (auto regno: protectedRegNo) {
        if (first) {
            protectedRegStr = PlatformArm32::regName[regno];
            first = false;
        } else {
            protectedRegStr += "," + PlatformArm32::regName[regno];
        }
    }

    if (!protectedRegStr.empty()) {
        iloc.inst("push", "{" + protectedRegStr + "}");
    }

    // 为fun分配栈帧，含局部变量、函数调用值传递的空间等
    iloc.allocStack(func, ARM32_TMP_REG_NO);
}

/// @brief 函数出口指令翻译成ARM32汇编
/// @param inst IR指令
void InstSelectorArm32::translate_exit(Instruction * inst)
{
    if (inst->getOperandsNum()) {
        // 存在返回值
        Value * retVal = inst->getOperand(0);

        // 赋值给寄存器R0
        iloc.load_var(0, retVal);
    }

    // 恢复栈空间
    iloc.inst("mov", "sp", "fp");

    // 保护寄存器的恢复
    auto & protectedRegStr = func->getProtectedRegStr();
    if (!protectedRegStr.empty()) {
        iloc.inst("pop", "{" + protectedRegStr + "}");
    }

    iloc.inst("bx", "lr");
}

/// @brief 赋值指令翻译成ARM32汇编
/// @param inst IR指令
void InstSelectorArm32::translate_assign(Instruction * inst)
{
    Value * result = inst->getOperand(0);
    Value * arg1 = inst->getOperand(1);

    int32_t arg1_regId = arg1->getRegId();
    int32_t result_regId = result->getRegId();

    if (arg1_regId != -1) {
        // 寄存器 => 内存
        // 寄存器 => 寄存器

        // r8 -> rs 可能用到r9
        iloc.store_var(arg1_regId, result, ARM32_TMP_REG_NO);
    } else if (result_regId != -1) {
        // 内存变量 => 寄存器

        iloc.load_var(result_regId, arg1);
    } else {
        // 内存变量 => 内存变量

        int32_t temp_regno = simpleRegisterAllocator.Allocate();

        // arg1 -> r8
        iloc.load_var(temp_regno, arg1);

        // r8 -> rs 可能用到r9
        iloc.store_var(temp_regno, result, ARM32_TMP_REG_NO);

        simpleRegisterAllocator.free(temp_regno);
    }
}

/// @brief 二元操作指令翻译成ARM32汇编
/// @param inst IR指令
/// @param operator_name 操作码
/// @param rs_reg_no 结果寄存器号
/// @param op1_reg_no 源操作数1寄存器号
/// @param op2_reg_no 源操作数2寄存器号
void InstSelectorArm32::translate_two_operator(Instruction * inst, string operator_name)
{
    Value * result = inst;
    Value * arg1 = inst->getOperand(0);
    Value * arg2 = inst->getOperand(1);

    int32_t arg1_reg_no = arg1->getRegId();
    int32_t arg2_reg_no = arg2->getRegId();
    int32_t result_reg_no = inst->getRegId();
    int32_t load_result_reg_no, load_arg1_reg_no, load_arg2_reg_no;

    // 看arg1是否是寄存器，若是则寄存器寻址，否则要load变量到寄存器中
    if (arg1_reg_no == -1) {

        // 分配一个寄存器r8
        load_arg1_reg_no = simpleRegisterAllocator.Allocate(arg1);

        // arg1 -> r8，这里可能由于偏移不满足指令的要求，需要额外分配寄存器
        iloc.load_var(load_arg1_reg_no, arg1);
    } else {
        load_arg1_reg_no = arg1_reg_no;
    }

    // 看arg2是否是寄存器，若是则寄存器寻址，否则要load变量到寄存器中
    if (arg2_reg_no == -1) {

        // 分配一个寄存器r9
        load_arg2_reg_no = simpleRegisterAllocator.Allocate(arg2);

        // arg2 -> r9
        iloc.load_var(load_arg2_reg_no, arg2);
    } else {
        load_arg2_reg_no = arg2_reg_no;
    }

    // 看结果变量是否是寄存器，若不是则需要分配一个新的寄存器来保存运算的结果
    if (result_reg_no == -1) {
        // 分配一个寄存器r10，用于暂存结果
        load_result_reg_no = simpleRegisterAllocator.Allocate(result);
    } else {
        load_result_reg_no = result_reg_no;
    }

    // r8 + r9 -> r10
    iloc.inst(operator_name,
              PlatformArm32::regName[load_result_reg_no],
              PlatformArm32::regName[load_arg1_reg_no],
              PlatformArm32::regName[load_arg2_reg_no]);

    // 结果不是寄存器，则需要把rs_reg_name保存到结果变量中
    if (result_reg_no == -1) {

        // 这里使用预留的临时寄存器，因为立即数可能过大，必须借助寄存器才可操作。

        // r10 -> result
        iloc.store_var(load_result_reg_no, result, ARM32_TMP_REG_NO);
    }

    // 释放寄存器
    simpleRegisterAllocator.free(arg1);
    simpleRegisterAllocator.free(arg2);
    simpleRegisterAllocator.free(result);
}

/// @brief 整数加法指令翻译成ARM32汇编
/// @param inst IR指令
void InstSelectorArm32::translate_add_int32(Instruction * inst)
{
    translate_two_operator(inst, "add");
}

/// @brief 整数减法指令翻译成ARM32汇编
/// @param inst IR指令
void InstSelectorArm32::translate_sub_int32(Instruction * inst)
{
    translate_two_operator(inst, "sub");
}

/// @brief 整数乘法指令翻译成ARM32汇编
/// @param inst IR指令
void InstSelectorArm32::translate_mul_int32(Instruction * inst)
{
    translate_two_operator(inst, "mul");
}

/// @brief 整数除法指令翻译成ARM32汇编
/// @param inst IR指令
void InstSelectorArm32::translate_div_int32(Instruction * inst)
{
    translate_two_operator(inst, "sdiv");
}

/// @brief 整数取余指令翻译成ARM32汇编
/// @param inst IR指令
void InstSelectorArm32::translate_mod_int32(Instruction * inst)
{
    // op0 = a, op1 = b, 结果放到 inst->getRegId()
    Value * a = inst->getOperand(0);
    Value * b = inst->getOperand(1);
    int rd = inst->getRegId();

    // 1) 先把 a 加载到目标寄存器 rd
    iloc.load_var(rd, a);

    // 2) 计算商 q： rd = sdiv rd, b
    iloc.inst("sdiv", PlatformArm32::regName[rd], PlatformArm32::regName[rd], PlatformArm32::regName[b->getRegId()]);

    // 3) 余数 = a - q*b
    iloc.inst("mls",
              PlatformArm32::regName[rd],
              PlatformArm32::regName[b->getRegId()],
              PlatformArm32::regName[rd],
              PlatformArm32::regName[a->getRegId()]);
}

/// @brief 函数调用指令翻译成ARM32汇编
/// @param inst IR指令
void InstSelectorArm32::translate_call(Instruction * inst)
{
    FuncCallInstruction * callInst = dynamic_cast<FuncCallInstruction *>(inst);

    int32_t operandNum = callInst->getOperandsNum();

    if (operandNum != realArgCount) {

        // 两者不一致 也可能没有ARG指令，正常
        if (realArgCount != 0) {

            minic_log(LOG_ERROR, "ARG指令的个数与调用函数个数不一致");
        }
    }

    if (operandNum) {

        // 强制占用这几个寄存器参数传递的寄存器
        simpleRegisterAllocator.Allocate(0);
        simpleRegisterAllocator.Allocate(1);
        simpleRegisterAllocator.Allocate(2);
        simpleRegisterAllocator.Allocate(3);

        // 前四个的后面参数采用栈传递
        int esp = 0;
        for (int32_t k = 4; k < operandNum; k++) {

            auto arg = callInst->getOperand(k);

            // 新建一个内存变量，用于栈传值到形参变量中
            MemVariable * newVal = func->newMemVariable((Type *) PointerType::get(arg->getType()));
            newVal->setMemoryAddr(ARM32_SP_REG_NO, esp);
            esp += 4;

            Instruction * assignInst = new MoveInstruction(func, newVal, arg);

            // 翻译赋值指令
            translate_assign(assignInst);

            delete assignInst;
        }

        for (int32_t k = 0; k < operandNum && k < 4; k++) {

            auto arg = callInst->getOperand(k);

            // 检查实参的类型是否是临时变量。
            // 如果是临时变量，该变量可更改为寄存器变量即可，或者设置寄存器号
            // 如果不是，则必须开辟一个寄存器变量，然后赋值即可

            Instruction * assignInst = new MoveInstruction(func, PlatformArm32::intRegVal[k], arg);

            // 翻译赋值指令
            translate_assign(assignInst);

            delete assignInst;
        }
    }

    iloc.call_fun(callInst->getName());

    if (operandNum) {
        simpleRegisterAllocator.free(0);
        simpleRegisterAllocator.free(1);
        simpleRegisterAllocator.free(2);
        simpleRegisterAllocator.free(3);
    }

    // 赋值指令
    if (callInst->hasResultValue()) {

        // 新建一个赋值操作
        Instruction * assignInst = new MoveInstruction(func, callInst, PlatformArm32::intRegVal[0]);

        // 翻译赋值指令
        translate_assign(assignInst);

        delete assignInst;
    }

    // 函数调用后清零，使得下次可正常统计
    realArgCount = 0;
}

///
/// @brief 实参指令翻译成ARM32汇编
/// @param inst
///
void InstSelectorArm32::translate_arg(Instruction * inst)
{
    // 翻译之前必须确保源操作数要么是寄存器，要么是内存，否则出错。
    Value * src = inst->getOperand(0);

    // 当前统计的ARG指令个数
    int32_t regId = src->getRegId();

    if (realArgCount < 4) {
        // 前四个参数
        if (regId != -1) {
            if (regId != realArgCount) {
                // 肯定寄存器分配有误
                minic_log(LOG_ERROR, "第%d个ARG指令对象寄存器分配有误: %d", argCount + 1, regId);
            }
        } else {
            minic_log(LOG_ERROR, "第%d个ARG指令对象不是寄存器", argCount + 1);
        }
    } else {
        // 必须是内存分配，若不是则出错
        int32_t baseRegId;
        bool result = src->getMemoryAddr(&baseRegId);
        if ((!result) || (baseRegId != ARM32_SP_REG_NO)) {

            minic_log(LOG_ERROR, "第%d个ARG指令对象不是SP寄存器寻址", argCount + 1);
        }
    }

    realArgCount++;
}

// 辅助：如果 v 已分配寄存器则返回，否则分配
int InstSelectorArm32::allocateOrGet(Value * v)
{
    if (v->getRegId() != -1)
        return v->getRegId();
    return simpleRegisterAllocator.Allocate(v);
}

// ------- CMP 布尔值生成: 具体实现 -------

void InstSelectorArm32::translate_cmp_lt(Instruction * inst)
{
    Value *a = inst->getOperand(0), *b = inst->getOperand(1);
    int rA = allocateOrGet(a), rB = allocateOrGet(b);
    iloc.load_var(rA, a);
    iloc.load_var(rB, b);
    iloc.inst("cmp", PlatformArm32::regName[rA], PlatformArm32::regName[rB]);
    simpleRegisterAllocator.free(a);
    simpleRegisterAllocator.free(b);
}

void InstSelectorArm32::translate_cmp_gt(Instruction * inst)
{
    Value * a = inst->getOperand(0);
    Value * b = inst->getOperand(1);

    // 1) 加载操作数到寄存器
    int regA = allocateOrGet(a);
    iloc.load_var(regA, a);
    int regB = allocateOrGet(b);
    iloc.load_var(regB, b);
    // 2) 直接发出 CMP 指令，更新 CPSR 标志
    iloc.inst("cmp", PlatformArm32::regName[regA], PlatformArm32::regName[regB]);
    // 3) 释放临时分配
    simpleRegisterAllocator.free(a);
    simpleRegisterAllocator.free(b);
}

void InstSelectorArm32::translate_cmp_le(Instruction * inst)
{
    Value *a = inst->getOperand(0), *b = inst->getOperand(1);
    int rA = allocateOrGet(a), rB = allocateOrGet(b);
    iloc.load_var(rA, a);
    iloc.load_var(rB, b);
    iloc.inst("cmp", PlatformArm32::regName[rA], PlatformArm32::regName[rB]);
    simpleRegisterAllocator.free(a);
    simpleRegisterAllocator.free(b);
}

void InstSelectorArm32::translate_cmp_ge(Instruction * inst)
{
    Value *a = inst->getOperand(0), *b = inst->getOperand(1);
    int rA = allocateOrGet(a), rB = allocateOrGet(b);
    iloc.load_var(rA, a);
    iloc.load_var(rB, b);
    iloc.inst("cmp", PlatformArm32::regName[rA], PlatformArm32::regName[rB]);
    simpleRegisterAllocator.free(a);
    simpleRegisterAllocator.free(b);
}

void InstSelectorArm32::translate_cmp_eq(Instruction * inst)
{
    Value *a = inst->getOperand(0), *b = inst->getOperand(1);
    int rA = allocateOrGet(a), rB = allocateOrGet(b);
    iloc.load_var(rA, a);
    iloc.load_var(rB, b);
    iloc.inst("cmp", PlatformArm32::regName[rA], PlatformArm32::regName[rB]);
    simpleRegisterAllocator.free(a);
    simpleRegisterAllocator.free(b);
}

void InstSelectorArm32::translate_cmp_ne(Instruction * inst)
{
    Value *a = inst->getOperand(0), *b = inst->getOperand(1);
    int rA = allocateOrGet(a), rB = allocateOrGet(b);
    iloc.load_var(rA, a);
    iloc.load_var(rB, b);
    iloc.inst("cmp", PlatformArm32::regName[rA], PlatformArm32::regName[rB]);
    simpleRegisterAllocator.free(a);
    simpleRegisterAllocator.free(b);
}

// ------- 条件跳转 -------
void InstSelectorArm32::translate_bc(Instruction * inst)
{
    auto * bc = dynamic_cast<BranchCondInstruction *>(inst);
    const string & L_true = bc->getTrueLabel()->getName();
    const string & L_false = bc->getFalseLabel()->getName();

    switch (inst->getOp()) {
        // 来自 CMP 的有符号/无符号比较直接用对应的 Bcc
        case IRInstOperator::IRINST_OP_BC_EQ:
            iloc.inst("beq", L_true);
            iloc.inst("b", L_false);
            return;
        case IRInstOperator::IRINST_OP_BC_NE:
            iloc.inst("bne", L_true);
            iloc.inst("b", L_false);
            return;
        case IRInstOperator::IRINST_OP_BC_LT:
            iloc.inst("blt", L_true);
            iloc.inst("b", L_false);
            return;
        case IRInstOperator::IRINST_OP_BC_LE:
            iloc.inst("ble", L_true);
            iloc.inst("b", L_false);
            return;
        case IRInstOperator::IRINST_OP_BC_GT:
            iloc.inst("bgt", L_true);
            iloc.inst("b", L_false);
            return;
        case IRInstOperator::IRINST_OP_BC_GE:
            iloc.inst("bge", L_true);
            iloc.inst("b", L_false);
            return;

        // 默认的布尔短路分支：如果第一个操作数非零跳真，否则跳假
        default: {
            Value * cond = bc->getOperand(0);
            int r = allocateOrGet(cond);
            iloc.load_var(r, cond);

            // CMP r, #0
            iloc.inst("cmp", PlatformArm32::regName[r], "#0");
            // BNE L_true
            iloc.inst("bne", L_true);
            // B  L_false
            iloc.inst("b", L_false);

            simpleRegisterAllocator.free(cond);
            return;
        }
    }
}

/// @brief 加载指令翻译成ARM32汇编
/// @param inst IR指令
void InstSelectorArm32::translate_load(Instruction * inst)
{
    Value * src = inst->getOperand(0); // 正确：LoadInstruction只有一个操作数，地址
    Value * dest = inst;               // 当前LoadInstruction本身就是目标值

    int dest_reg_no = allocateOrGet(dest);
    int base_reg_no;
    int64_t offset;

    if (src->getMemoryAddr(&base_reg_no, &offset)) {
        // 加载数组元素
        iloc.load_array_element(dest_reg_no, base_reg_no, offset);
    } else {
        // 加载普通变量
        iloc.load_var(dest_reg_no, src);
    }

    simpleRegisterAllocator.free(dest);
}

/// @brief 存储指令翻译成ARM32汇编
/// @param inst IR指令
void InstSelectorArm32::translate_store(Instruction * inst)
{
    Value * dest = inst->getOperand(0); // 目标地址
    Value * src = inst->getOperand(1);  // 源寄存器或变量

    int src_reg_no = allocateOrGet(src);
    int base_reg_no;
    int64_t offset;

    if (dest->getMemoryAddr(&base_reg_no, &offset)) {
        // 存储数组元素
        iloc.store_array_element(src_reg_no, base_reg_no, offset, ARM32_TMP_REG_NO);
    } else {
        // 存储普通变量
        iloc.store_var(src_reg_no, dest, ARM32_TMP_REG_NO);
    }

    simpleRegisterAllocator.free(src);
}